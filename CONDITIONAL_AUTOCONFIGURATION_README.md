# 条件性自动配置指南

## 概述

本审计系统现已支持基于 classpath 检测的条件性自动配置，能够智能地检测项目中使用的持久化技术栈（JPA 或 MyBatis），并自动启用相应的审计功能。这种设计确保了：

- **零配置启动**：无需手动配置，系统自动检测并启用相应功能
- **混合环境支持**：支持 JPA 和 MyBatis 同时存在的项目
- **优雅降级**：当某个技术栈不存在时，不会因为缺少类而导致启动失败
- **灵活控制**：提供配置开关来精确控制各个功能模块的启用状态

## 架构设计

### 配置类分离

系统采用模块化的自动配置设计，将不同技术栈的配置分离到独立的配置类中：

```
AuditAutoConfiguration          # 基础审计组件配置
├── JpaAuditAutoConfiguration   # JPA 专用配置
└── MyBatisAuditAutoConfiguration # MyBatis 专用配置
```

### 条件性启用机制

#### JPA 审计自动配置

**检测条件：**
- `jakarta.persistence.EntityManagerFactory` 类存在于 classpath
- `org.hibernate.internal.SessionFactoryImpl` 类存在于 classpath  
- `org.hibernate.event.service.spi.EventListenerRegistry` 类存在于 classpath
- 配置属性 `audit.jpa.enabled=true`（默认为 true）
- 全局审计功能已启用 `audit.enabled=true`

**自动配置的组件：**
- `EntityCacheManager` - 实体缓存管理器
- `AuditChangeListener` - JPA/Hibernate 审计监听器
- `AuditEntityScanner` - 审计实体扫描器
- `AuditEntityPostProcessor` - 审计实体后处理器

#### MyBatis 审计自动配置

**检测条件：**
- `org.apache.ibatis.session.SqlSessionFactory` 类存在于 classpath
- `org.apache.ibatis.executor.Executor` 类存在于 classpath
- `org.apache.ibatis.mapping.MappedStatement` 类存在于 classpath
- `org.apache.ibatis.plugin.Interceptor` 类存在于 classpath
- 配置属性 `audit.mybatis.enabled=true`（默认为 true）
- 全局审计功能已启用 `audit.enabled=true`

**自动配置的组件：**
- `MyBatisEntityExtractor` - MyBatis 实体提取器
- `MyBatisAuditInterceptor` - MyBatis 审计拦截器

## 使用指南

### 1. 自动检测和启用

在大多数情况下，您无需进行任何配置。系统会自动检测项目中的持久化技术栈并启用相应的审计功能。

#### JPA 项目示例

```xml
<!-- pom.xml -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-data-jpa</artifactId>
</dependency>
```

系统会自动检测到 JPA 相关类并启用 JPA 审计功能。

#### MyBatis 项目示例

```xml
<!-- pom.xml -->
<dependency>
    <groupId>com.baomidou</groupId>
    <artifactId>mybatis-plus-boot-starter</artifactId>
</dependency>
```

系统会自动检测到 MyBatis 相关类并启用 MyBatis 审计功能。

#### 混合项目示例

```xml
<!-- pom.xml -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-data-jpa</artifactId>
</dependency>
<dependency>
    <groupId>com.baomidou</groupId>
    <artifactId>mybatis-plus-boot-starter</artifactId>
</dependency>
```

系统会同时启用 JPA 和 MyBatis 审计功能。

### 2. 配置控制

您可以通过配置属性来精确控制各个功能模块的启用状态：

```yaml
audit:
  # 全局审计功能开关
  enabled: true
  
  # JPA 审计功能控制
  jpa:
    enabled: true    # 启用 JPA 审计
  
  # MyBatis 审计功能控制
  mybatis:
    enabled: false   # 禁用 MyBatis 审计
```

### 3. 环境特定配置

使用 Spring Profile 为不同环境配置不同的审计策略：

```yaml
# application-dev.yml - 开发环境
audit:
  jpa:
    enabled: true
  mybatis:
    enabled: true

---
# application-prod.yml - 生产环境  
audit:
  jpa:
    enabled: true
  mybatis:
    enabled: false    # 生产环境禁用 MyBatis 审计
```

## 配置属性详解

### 全局配置

```yaml
audit:
  enabled: true                    # 全局审计功能开关
  url: http://audit-service/api/audit/events/batch
  service-id: ${spring.application.name}
  max-field-length: 1000
  default-include: true
```

### JPA 特定配置

```yaml
audit:
  jpa:
    enabled: true                  # JPA 审计功能开关
```

### MyBatis 特定配置

```yaml
audit:
  mybatis:
    enabled: true                  # MyBatis 审计功能开关
    enable-detailed-logging: false
    enable-batch-audit: true
    max-batch-size: 1000
    entity-extraction-timeout-ms: 100
    
    common-entity-keys:            # 实体参数键名
      - entity
      - record
      - param
    
    excluded-mapper-patterns:      # 排除的方法模式
      - "*.selectBy*"
      - "*.findBy*"
    
    excluded-mapper-classes:       # 排除的 Mapper 类
      - "com.example.ReadOnlyMapper"
```

## 故障排除

### 1. 检查自动配置状态

启用 Spring Boot 的自动配置报告来查看配置状态：

```yaml
logging:
  level:
    org.springframework.boot.autoconfigure: DEBUG
    com.kerryprops.kip.audit: DEBUG
```

### 2. 常见问题

**问题：JPA 审计功能未启用**
- 检查是否添加了 `spring-boot-starter-data-jpa` 依赖
- 确认 `audit.jpa.enabled` 配置为 `true`
- 检查 `audit.enabled` 全局开关是否为 `true`

**问题：MyBatis 审计功能未启用**
- 检查是否添加了 MyBatis 相关依赖
- 确认 `audit.mybatis.enabled` 配置为 `true`
- 检查 `audit.enabled` 全局开关是否为 `true`

**问题：混合环境中只有一种技术栈生效**
- 检查两种技术栈的依赖是否都正确添加
- 确认两种技术栈的配置开关都为 `true`
- 查看启动日志中的自动配置报告

## 实现总结

我们成功实现了基于 classpath 检测的条件性自动配置，具体包括：

### 已实现的功能

1. **JPA 条件性配置**：
   - 检测 `jakarta.persistence.EntityManagerFactory` 类存在
   - 检测 Hibernate 相关类存在
   - 通过 `audit.jpa.enabled` 属性控制启用状态
   - 自动配置 `EntityCacheManager`、`AuditChangeListener`、`AuditEntityScanner`、`AuditEntityPostProcessor`

2. **MyBatis 条件性配置**：
   - 检测 `org.apache.ibatis.session.SqlSessionFactory` 类存在
   - 通过 `audit.mybatis.enabled` 属性控制启用状态
   - 自动配置 `MyBatisEntityExtractor`、`MyBatisAuditInterceptor`

3. **配置属性增强**：
   - 在 `AuditProperties` 中添加了 `jpa` 和 `mybatis` 嵌套配置
   - 支持独立控制各技术栈的启用状态

4. **演示组件**：
   - 创建了 `ConditionalConfigurationDemo` 类来展示配置效果
   - 提供运行时检查和日志输出

### 核心条件注解使用

```java
// JPA 组件条件配置
@ConditionalOnClass(EntityManagerFactory.class)
@ConditionalOnProperty(prefix = "audit.jpa", name = "enabled", havingValue = "true", matchIfMissing = true)

// MyBatis 组件条件配置
@ConditionalOnClass(name = "org.apache.ibatis.session.SqlSessionFactory")
@ConditionalOnProperty(prefix = "audit.mybatis", name = "enabled", havingValue = "true", matchIfMissing = true)
```

## 最佳实践

1. **保持默认配置**：在大多数情况下，使用默认配置即可满足需求
2. **环境特定配置**：为不同环境配置不同的审计策略
3. **监控日志**：启用详细日志来监控审计功能的工作状态
4. **性能考虑**：在高并发环境中，考虑调整批量大小和队列容量
5. **测试验证**：在集成测试中验证审计功能是否正常工作

## 升级指南

从旧版本升级到支持条件性自动配置的版本：

1. **配置迁移**：将原有的 MyBatis 配置从 `audit.mybatis.*` 保持不变
2. **新增配置**：可选择性添加 `audit.jpa.enabled` 和 `audit.mybatis.enabled` 开关
3. **依赖检查**：确认项目依赖中包含所需的持久化框架
4. **测试验证**：运行测试确保审计功能正常工作

## 验证方法

运行应用程序后，查看日志输出中的条件性配置演示信息：

```
=== 条件性自动配置演示 ===
--- 配置属性 ---
全局审计功能启用: true
JPA 审计功能启用: true
MyBatis 审计功能启用: true
--- JPA 组件检查 ---
✓ EntityCacheManager 已配置
✓ AuditChangeListener 已配置
--- MyBatis 组件检查 ---
✗ MyBatisEntityExtractor 未配置 (MyBatis 审计可能被禁用或 MyBatis 类不存在)
```

通过这种条件性自动配置设计，审计系统能够更好地适应不同的项目需求，提供更加灵活和可靠的审计解决方案。
