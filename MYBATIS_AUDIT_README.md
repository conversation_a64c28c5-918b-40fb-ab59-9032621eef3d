# MyBatis 审计功能集成指南

## 概述

本项目现已支持 MyBatis 技术栈的数据审计功能，通过 MyBatis 拦截器机制实现对 INSERT、UPDATE、DELETE 操作的自动审计。该功能与现有的 JPA 审计框架无缝集成，提供统一的审计体验。

## 核心特性

### 🚀 主要功能
- **自动拦截**: 自动拦截 MyBatis 的 INSERT、UPDATE、DELETE 操作
- **实体提取**: 智能从 MyBatis 参数中提取实体对象
- **无缝集成**: 与现有审计框架完全兼容，使用相同的配置和注解
- **性能优化**: 异步处理，不影响业务性能
- **灵活配置**: 支持排除特定 Mapper 方法或类

### 🎯 技术栈支持
- MyBatis 3.5.15+
- MyBatis Plus 3.5.5+
- MyBatis Spring 3.0.3+
- Spring Boot 3.2.5+

## 快速开始

### 1. 添加依赖

MyBatis 相关依赖已包含在项目中（可选依赖）：

```xml
<!-- MyBatis Plus -->
<dependency>
    <groupId>com.baomidou</groupId>
    <artifactId>mybatis-plus-boot-starter</artifactId>
    <version>3.5.5</version>
    <optional>true</optional>
</dependency>

<!-- MyBatis Spring -->
<dependency>
    <groupId>org.mybatis</groupId>
    <artifactId>mybatis-spring</artifactId>
    <version>3.0.3</version>
    <optional>true</optional>
</dependency>
```

### 2. 实体类配置

使用现有的 `@AuditEntity` 和 `@AuditField` 注解标记需要审计的实体：

```java
@AuditEntity(
    excludeFields = {"internalVersion"},
    defaultInclude = true,
    description = "产品实体"
)
public class Product {
    
    private Long id;
    
    @AuditField(alias = "产品名称", include = true)
    private String name;
    
    @AuditField(alias = "产品价格")
    private BigDecimal price;
    
    @AuditField(exclude = true)
    private String internalData;
    
    // getters and setters...
}
```

### 3. Mapper 接口

创建标准的 MyBatis Mapper 接口：

```java
@Mapper
public interface ProductMapper {
    
    @Insert("INSERT INTO products (name, price) VALUES (#{name}, #{price})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertProduct(Product product);
    
    @Update("UPDATE products SET name = #{name}, price = #{price} WHERE id = #{id}")
    int updateProduct(Product product);
    
    @Delete("DELETE FROM products WHERE id = #{id}")
    int deleteProduct(Long id);
    
    // 查询操作不会触发审计
    @Select("SELECT * FROM products WHERE id = #{id}")
    Product selectById(Long id);
}
```

### 4. 应用配置

在 `application.yml` 中配置 MyBatis 审计参数：

```yaml
audit:
  # 基础审计配置
  enabled: true
  url: http://audit-service/api/audit/events/batch
  
  # MyBatis 审计配置
  mybatis:
    enabled: true                    # 启用 MyBatis 审计
    enable-detailed-logging: true    # 启用详细日志
    enable-batch-audit: true         # 启用批量操作审计
    max-batch-size: 1000            # 批量操作最大记录数
    
    # 实体参数键名配置
    common-entity-keys:
      - entity
      - record
      - param
      - data
      - model
    
    # 排除的 Mapper 方法模式
    excluded-mapper-patterns:
      - "*.selectBy*"
      - "*.findBy*"
      - "*.countBy*"
      - "*.existsBy*"
      - "*.queryBy*"
    
    # 排除的 Mapper 类
    excluded-mapper-classes:
      - "com.example.ReadOnlyMapper"
```

## 核心组件

### MyBatisAuditInterceptor
主要的 MyBatis 拦截器，负责：
- 拦截 `Executor.update` 方法
- 识别 INSERT、UPDATE、DELETE 操作
- 调用审计服务记录变更

### MyBatisEntityExtractor
实体提取器，负责：
- 从 MyBatis 参数中智能提取实体对象
- 支持直接实体、Map 参数、Collection 参数
- 递归检查复杂对象结构

### MyBatisAuditProperties
配置属性类，提供：
- MyBatis 审计功能开关
- 实体提取配置
- Mapper 方法排除规则

## 工作原理

### 1. 拦截机制
```
MyBatis SQL 执行
       ↓
MyBatisAuditInterceptor 拦截
       ↓
检查操作类型 (INSERT/UPDATE/DELETE)
       ↓
提取实体对象
       ↓
调用 AuditService
       ↓
异步记录审计日志
```

### 2. 实体提取策略
1. **直接实体**: 参数本身就是带 `@AuditEntity` 注解的实体
2. **Map 参数**: 从 Map 的值中查找实体对象
3. **Collection 参数**: 从集合的元素中提取实体
4. **复杂对象**: 通过反射从对象字段中提取实体

### 3. 审计时机
- **INSERT**: 操作执行后记录创建审计
- **UPDATE**: 操作前缓存原始状态，操作后记录更新审计
- **DELETE**: 操作前记录删除审计

## 配置详解

### 基础配置
```yaml
audit:
  mybatis:
    enabled: true                    # 是否启用 MyBatis 审计
    enable-detailed-logging: false   # 是否启用详细日志
    enable-batch-audit: true         # 是否启用批量操作审计
    max-batch-size: 1000            # 批量操作最大记录数
    entity-extraction-timeout-ms: 100 # 实体提取超时时间
```

### 实体提取配置
```yaml
audit:
  mybatis:
    common-entity-keys:              # 常见的实体参数键名
      - entity
      - record
      - param
      - data
      - model
      - object
      - item
```

### 排除配置
```yaml
audit:
  mybatis:
    excluded-mapper-patterns:        # 排除的方法模式
      - "*.selectBy*"
      - "*.findBy*"
      - "*.countBy*"
      - "*.existsBy*"
      - "*.queryBy*"
    
    excluded-mapper-classes:         # 排除的 Mapper 类
      - "com.example.ReadOnlyMapper"
      - "com.example.ReportMapper"
```

## 最佳实践

### 1. 实体设计
- 确保实体类标注 `@AuditEntity` 注解
- 合理使用 `@AuditField` 控制字段审计
- 排除敏感字段和内部字段

### 2. Mapper 设计
- 使用清晰的方法命名
- 避免在查询方法中使用 UPDATE/DELETE 操作
- 合理使用批量操作

### 3. 性能优化
- 配置合适的批量大小限制
- 排除不需要审计的 Mapper 方法
- 启用异步处理

### 4. 监控和调试
- 启用详细日志进行问题排查
- 监控审计队列长度和处理延迟
- 定期检查审计记录的完整性

## 故障排查

### 常见问题

**Q: MyBatis 操作没有触发审计**
A: 检查以下几点：
1. 实体类是否标注 `@AuditEntity` 注解
2. MyBatis 审计是否启用 (`audit.mybatis.enabled=true`)
3. 方法是否被排除规则匹配
4. 参数中是否包含可审计的实体对象

**Q: 实体提取失败**
A: 检查以下几点：
1. 参数结构是否复杂，考虑增加超时时间
2. 实体是否正确嵌套在参数中
3. 是否需要添加自定义的实体键名

**Q: 批量操作审计失败**
A: 检查以下几点：
1. 是否启用批量审计 (`enable-batch-audit=true`)
2. 批量大小是否超过限制 (`max-batch-size`)
3. 集合中的实体是否都标注了审计注解

### 调试配置
```yaml
logging:
  level:
    com.kerryprops.kip.audit.mybatis: DEBUG
    org.apache.ibatis: DEBUG
```

## 示例代码

完整的示例代码请参考测试包中的：
- `TestProduct.java` - 示例实体类
- `TestProductMapper.java` - 示例 Mapper 接口
- `MyBatisAuditIntegrationTest.java` - 集成测试

## 版本兼容性

| 组件 | 版本要求 |
|------|----------|
| Spring Boot | 3.2.5+ |
| MyBatis | 3.5.15+ |
| MyBatis Plus | 3.5.5+ |
| MyBatis Spring | 3.0.3+ |
| Java | 17+ |

## 更新日志

### v1.0.4
- ✅ 新增 MyBatis 审计功能
- ✅ 支持 INSERT、UPDATE、DELETE 操作拦截
- ✅ 智能实体提取机制
- ✅ 灵活的配置和排除规则
- ✅ 完整的测试覆盖
