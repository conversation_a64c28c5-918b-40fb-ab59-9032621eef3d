# 审计系统条件性自动配置示例
# 本配置文件展示了如何通过配置属性来控制 JPA 和 MyBatis 审计功能的启用

audit:
  # 全局审计功能开关（默认: true）
  enabled: true
  
  # 审计服务端点
  url: http://audit-service/api/audit/events/batch
  
  # 服务标识
  service-id: ${spring.application.name}
  
  # 全局配置
  max-field-length: 1000
  default-include: true
  
  # JPA 审计功能配置
  jpa:
    # 是否启用 JPA 审计功能（默认: true）
    # 当 classpath 中存在 jakarta.persistence.EntityManagerFactory 时自动启用
    enabled: true
  
  # MyBatis 审计功能配置  
  mybatis:
    # 是否启用 MyBatis 审计功能（默认: true）
    # 当 classpath 中存在 org.apache.ibatis.session.SqlSessionFactory 时自动启用
    enabled: true
    
    # MyBatis 特定配置
    enable-detailed-logging: false
    enable-batch-audit: true
    max-batch-size: 1000
    entity-extraction-timeout-ms: 100
    
    # 实体参数键名配置
    common-entity-keys:
      - entity
      - record
      - param
      - data
      - model
      - object
      - item
    
    # 排除的 Mapper 方法模式
    excluded-mapper-patterns:
      - "*.selectBy*"
      - "*.findBy*"
      - "*.countBy*"
      - "*.existsBy*"
      - "*.queryBy*"
    
    # 排除的 Mapper 类
    excluded-mapper-classes:
      - "com.example.ReadOnlyMapper"
  
  # 队列配置
  queue:
    capacity: 10000
    batch-size: 100
  
  # HTTP 客户端配置
  http-client:
    connect-timeout: 5000
    read-timeout: 10000
  
  # 请求头配置
  header:
    user: "X-User"
    ui-model: "X-UI-Model"
    conversation-id: "X-Conversation-Id"
    correlation-id: "X-Correlation-ID"
    audit-filter-key: "X-Audit-Filter-Key"
    audit-filter-value: "X-Audit-Filter-Value"

---
# 仅启用 JPA 审计的配置示例
spring:
  profiles: jpa-only

audit:
  jpa:
    enabled: true
  mybatis:
    enabled: false

---
# 仅启用 MyBatis 审计的配置示例  
spring:
  profiles: mybatis-only

audit:
  jpa:
    enabled: false
  mybatis:
    enabled: true

---
# 禁用所有审计功能的配置示例
spring:
  profiles: no-audit

audit:
  enabled: false
