package com.kerryprops.kip.audit;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import com.kerryprops.kip.audit.mybatis.MyBatisAuditInterceptor;
import com.kerryprops.kip.audit.mybatis.MyBatisEntityExtractor;

/**
 * 条件性自动配置演示类
 * 
 * 这个类演示了审计系统如何根据 classpath 中存在的类和配置属性
 * 来条件性地启用 JPA 和 MyBatis 审计功能。
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class ConditionalConfigurationDemo implements CommandLineRunner {

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private AuditProperties auditProperties;

    @Override
    public void run(String... args) throws Exception {
        log.info("=== 条件性自动配置演示 ===");
        
        // 显示配置属性
        showConfigurationProperties();
        
        // 检查 JPA 相关组件
        checkJpaComponents();
        
        // 检查 MyBatis 相关组件
        checkMyBatisComponents();
        
        // 显示所有审计相关的 Bean
        showAuditBeans();
        
        log.info("=== 演示结束 ===");
    }

    private void showConfigurationProperties() {
        log.info("--- 配置属性 ---");
        log.info("全局审计功能启用: {}", true); // 由于能运行到这里，说明全局审计已启用
        log.info("JPA 审计功能启用: {}", auditProperties.getJpa().isEnabled());
        log.info("MyBatis 审计功能启用: {}", auditProperties.getMybatis().isEnabled());
    }

    private void checkJpaComponents() {
        log.info("--- JPA 组件检查 ---");
        
        // 检查 EntityCacheManager
        if (applicationContext.getBeanNamesForType(EntityCacheManager.class).length > 0) {
            log.info("✓ EntityCacheManager 已配置");
        } else {
            log.info("✗ EntityCacheManager 未配置 (JPA 审计可能被禁用或 JPA 类不存在)");
        }
        
        // 检查 AuditChangeListener
        if (applicationContext.getBeanNamesForType(AuditChangeListener.class).length > 0) {
            log.info("✓ AuditChangeListener 已配置");
        } else {
            log.info("✗ AuditChangeListener 未配置 (JPA 审计可能被禁用)");
        }
        
        // 检查 AuditEntityScanner
        if (applicationContext.getBeanNamesForType(AuditEntityScanner.class).length > 0) {
            log.info("✓ AuditEntityScanner 已配置");
        } else {
            log.info("✗ AuditEntityScanner 未配置 (JPA 审计可能被禁用)");
        }
        
        // 检查 AuditEntityPostProcessor
        if (applicationContext.getBeanNamesForType(AuditEntityPostProcessor.class).length > 0) {
            log.info("✓ AuditEntityPostProcessor 已配置");
        } else {
            log.info("✗ AuditEntityPostProcessor 未配置 (JPA 审计可能被禁用或 Hibernate 类不存在)");
        }
    }

    private void checkMyBatisComponents() {
        log.info("--- MyBatis 组件检查 ---");
        
        // 检查 MyBatisEntityExtractor
        if (applicationContext.getBeanNamesForType(MyBatisEntityExtractor.class).length > 0) {
            log.info("✓ MyBatisEntityExtractor 已配置");
        } else {
            log.info("✗ MyBatisEntityExtractor 未配置 (MyBatis 审计可能被禁用或 MyBatis 类不存在)");
        }
        
        // 检查 MyBatisAuditInterceptor
        if (applicationContext.getBeanNamesForType(MyBatisAuditInterceptor.class).length > 0) {
            log.info("✓ MyBatisAuditInterceptor 已配置");
        } else {
            log.info("✗ MyBatisAuditInterceptor 未配置 (MyBatis 审计可能被禁用或 MyBatis 类不存在)");
        }
    }

    private void showAuditBeans() {
        log.info("--- 所有审计相关 Bean ---");
        
        String[] beanNames = applicationContext.getBeanDefinitionNames();
        int auditBeanCount = 0;
        
        for (String beanName : beanNames) {
            if (beanName.toLowerCase().contains("audit") || 
                beanName.toLowerCase().contains("mybatis")) {
                try {
                    Object bean = applicationContext.getBean(beanName);
                    log.info("Bean: {} -> {}", beanName, bean.getClass().getSimpleName());
                    auditBeanCount++;
                } catch (Exception e) {
                    log.debug("无法获取 Bean: {} - {}", beanName, e.getMessage());
                }
            }
        }
        
        log.info("总共发现 {} 个审计相关的 Bean", auditBeanCount);
    }
}
